# Enable Persian Language with Font Support
init python:
    # Simple function to enable Persian
    def enable_persian():
        original_known = renpy.known_languages
        def new_known():
            langs = list(original_known())
            if "fa" not in langs:
                langs.append("fa")
            return langs
        renpy.known_languages = new_known

    enable_persian()

# Persian font configuration
init -1 python:
    # Set up Persian font support
    def setup_persian_font():
        # Use system fonts for Persian
        config.font_replacement_map["DejaVuSans.ttf", False, True] = ("arial.ttf", False, False)
        config.font_replacement_map["DejaVuSans.ttf", False, False] = ("arial.ttf", False, False)

        # Enable font substitution for Persian characters
        config.font_hinting = "auto"
        config.font_replacement_map = {
            # Map default fonts to system fonts that support Persian
            ("DejaVuSans.ttf", False, False): ("arial.ttf", <PERSON>als<PERSON>, <PERSON>alse),
            ("DejaVuSans.ttf", True, False): ("arial.ttf", True, False),
        }

    setup_persian_font()

# Add a simple way to switch to Persian
label start_persian:
    $ renpy.change_language("fa")
    return

# Create a simple screen for language selection
screen persian_language_menu():
    modal True
    frame:
        xalign 0.5
        yalign 0.5
        vbox:
            text "انتخاب زبان / Select Language"
            textbutton "فارسی" action [Language("fa"), Hide("persian_language_menu")]
            textbutton "English" action [Language(None), Hide("persian_language_menu")]
            textbutton "Español" action [Language("es"), Hide("persian_language_menu")]
            textbutton "بستن / Close" action Hide("persian_language_menu")

# Add key binding
init:
    $ config.keymap['persian_menu'] = ['shift_K_p']
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))

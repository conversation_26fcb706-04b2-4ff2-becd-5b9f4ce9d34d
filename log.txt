2025-08-03 18:11:08 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Becoming a Femboy
3.6.2-default
Built at 2025-06-19 17:46:17 UTC

Early init took 0.40s
Loading error handling took 0.11s
Loading script took 2.67s
Loading save slot metadata took 0.06s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)
Running init code took 0.23s
Loading analysis data took 0.03s
Analyze and compile ATL took 0.02s
Reloading save slot metadata took 0.04s
Index archives took 0.00s
Dump and make backups took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.10s
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.02s
Save screen analysis took 0.03s
Prepare screens took 0.13s
Save pyanalysis. took 0.00s
Save bytecode. took 0.07s
Running _start took 0.00s
Interface start took 0.62s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096

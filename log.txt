2025-08-03 18:19:04 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Becoming a Femboy
3.6.2-default
Built at 2025-06-19 17:46:17 UTC

Early init took 0.41s
Loading error handling took 0.08s
Loading script took 4.59s
Loading save slot metadata took 0.07s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)
Running init code took 0.24s
Loading analysis data took 0.00s
Analyze and compile ATL took 0.04s
Reloading save slot metadata took 0.04s
Index archives took 0.00s
Dump and make backups took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.09s
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.05s
Save screen analysis took 0.04s
Prepare screens took 0.30s
Save pyanalysis. took 0.03s
Save bytecode. took 0.07s
Running _start took 0.00s
Interface start took 0.63s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096

Full traceback:
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "scripts/screens/splashscreen.rpyc", line 5, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 1374, in execute
    renpy.exports.with_statement(trans, paired=paired)
  File "C:/Users\The Doctor\Music\362/renpy/exports/statementexports.py", line 257, in with_statement
    return renpy.game.interface.do_with(trans, paired, clear=clear)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1581, in do_with
    return self.interact(trans_pause=True,
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/transition.py", line 384, in render
    top = render(self.new_widget, width, height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

While running game code:
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
AttributeError: 'str' object has no attribute 'get'
While handling exception:
Traceback (most recent call last):
  File "C:/Users\The Doctor\Music\362/renpy/execution.py", line 599, in run
    node.execute()
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 1374, in execute
    renpy.exports.with_statement(trans, paired=paired)
  File "C:/Users\The Doctor\Music\362/renpy/exports/statementexports.py", line 257, in with_statement
    return renpy.game.interface.do_with(trans, paired, clear=clear)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1581, in do_with
    return self.interact(trans_pause=True,
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/transition.py", line 384, in render
    top = render(self.new_widget, width, height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:/Users\The Doctor\Music\362/renpy/display/error.py", line 140, in report_exception
    renpy.game.invoke_in_new_context(
  File "C:/Users\The Doctor\Music\362/renpy/game.py", line 303, in invoke_in_new_context
    return callable(*args, **kwargs)
  File "C:/Users\The Doctor\Music\362/renpy/display/error.py", line 48, in call_exception_screen
    return renpy.ui.interact(mouse="screen", type="screen", suppress_overlay=True, suppress_underlay=True)
  File "C:/Users\The Doctor\Music\362/renpy/ui.py", line 301, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1974, in render
    cwidth, cheight = sizeit('c', width, height, 0, 0)
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1971, in sizeit
    rend = renpy.display.render.render_for_size(pos_d[pos], width, height, st, at)
  File "render.pyx", line 312, in renpy.display.render.render_for_size
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1974, in render
    cwidth, cheight = sizeit('c', width, height, 0, 0)
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1971, in sizeit
    rend = renpy.display.render.render_for_size(pos_d[pos], width, height, st, at)
  File "render.pyx", line 312, in renpy.display.render.render_for_size
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/viewport.py", line 287, in render
    surf = renpy.display.render.render(self.child, child_width, child_height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

Full traceback:
  File "C:/Users\The Doctor\Music\362/renpy/bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 652, in main
    run(restart)
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 148, in run
    renpy.execution.run_context(True)
  File "C:/Users\The Doctor\Music\362/renpy/execution.py", line 958, in run_context
    context.run()
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "scripts/screens/splashscreen.rpyc", line 5, in script
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "scripts/screens/splashscreen.rpyc", line 5, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 1374, in execute
    renpy.exports.with_statement(trans, paired=paired)
  File "C:/Users\The Doctor\Music\362/renpy/exports/statementexports.py", line 257, in with_statement
    return renpy.game.interface.do_with(trans, paired, clear=clear)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1581, in do_with
    return self.interact(trans_pause=True,
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/transition.py", line 384, in render
    top = render(self.new_widget, width, height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

While running game code:
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
AttributeError: 'str' object has no attribute 'get'

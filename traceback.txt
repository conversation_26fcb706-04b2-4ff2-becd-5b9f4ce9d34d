﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/persian_font.rpy", line 63, in <module>
Exception: config.after_change_language_callbacks is not a known configuration variable.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "C:/Users\The Doctor\Music\362/renpy/bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 541, in main
    renpy.game.context().run(node)
  File "game/persian_font.rpyc", line 54, in script
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "game/persian_font.rpyc", line 54, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/persian_font.rpy", line 63, in <module>
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.after_change_language_callbacks is not a known configuration variable.

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Sun Aug  3 22:48:12 2025

# Language Configuration
# This file adds Persian (Farsi) language support to the game

init -1000 python:
    # Define language names for display in a global variable
    language_names = {
        None: "English",
        "es": "Español",
        "pt": "Português",
        "ru": "Русский",
        "fr": "Français",
        "sv": "Svenska",
        "fa": "فارسی"  # Persian/Farsi
    }

# Override the preferences screen to include Persian
init 1000 python:
    # Custom language preference that includes Persian
    def get_available_languages():
        """Get list of available languages including Persian"""
        languages = []

        # Add default language (English)
        languages.append((language_names.get(None, "English"), None))

        # Add translated languages that exist
        for lang_code in ["es", "pt", "ru", "fr", "sv", "fa"]:
            if lang_code in renpy.known_languages() or lang_code == "fa":
                lang_name = language_names.get(lang_code, lang_code)
                languages.append((lang_name, lang_code))

        return languages

# Add Persian language support by modifying the language action
init 1001 python:
    # Override the Language action to support Persian
    import renpy.exports as renpy

    # Store original known_languages function
    original_known_languages = renpy.known_languages

    def patched_known_languages():
        """Return known languages including Persian"""
        langs = set(original_known_languages())
        langs.add("fa")  # Add Persian
        return langs

    # Replace the function
    renpy.known_languages = patched_known_languages

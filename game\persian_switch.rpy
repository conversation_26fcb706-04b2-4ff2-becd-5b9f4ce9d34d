# Persian Language Switch
# Simple file to enable Persian language

# Add Persian language support
init -1000 python:
    # Patch known_languages to include Persian
    original_known_languages = renpy.known_languages
    
    def new_known_languages():
        langs = list(original_known_languages())
        if "fa" not in langs:
            langs.append("fa")
        return langs
    
    renpy.known_languages = new_known_languages

# Add a label to switch to Persian
label switch_to_persian:
    $ renpy.change_language("fa")
    return

# Add a key binding to quickly switch to Persian
init:
    $ config.keymap['persian'] = ['p']
    $ config.underlay.append(renpy.Keymap(persian=renpy.Rollback(0, force=True, greedy=False, label="switch_to_persian")))

# Create a simple screen to show language options
screen language_selector():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        padding (50, 30)
        
        vbox:
            spacing 10
            
            text "Select Language:" size 30
            
            textbutton "English" action [Language(None), Hide("language_selector")]
            textbutton "Español" action [Language("es"), Hide("language_selector")]  
            textbutton "Português" action [Language("pt"), Hide("language_selector")]
            textbutton "Русский" action [Language("ru"), Hide("language_selector")]
            textbutton "Français" action [Language("fr"), Hide("language_selector")]
            textbutton "Svenska" action [Language("sv"), Hide("language_selector")]
            textbutton "فارسی" action [Language("fa"), Hide("language_selector")]
            
            textbutton "Cancel" action Hide("language_selector")

# Add key binding to show language selector
init:
    $ config.keymap['show_languages'] = ['shift_K_l']
    $ config.underlay.append(renpy.Keymap(show_languages=Show("language_selector")))

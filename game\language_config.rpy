# Language Configuration
# This file adds Persian (Farsi) language support to the game

init -1000 python:
    # Add Persian language to the available languages
    # This ensures Persian appears in the language selection menu
    
    # Define language names for display
    config.language_names = {
        None: "English",
        "es": "Español", 
        "pt": "Português",
        "ru": "Русский",
        "fr": "Français",
        "sv": "Svenska",
        "fa": "فارسی"  # Persian/Farsi
    }
    
    # Ensure Persian is recognized as a known language
    def add_persian_language():
        # Force Ren'Py to recognize Persian as an available language
        if hasattr(renpy, 'known_languages'):
            known_langs = renpy.known_languages()
            if 'fa' not in known_langs:
                # This will be handled by <PERSON>'Py's translation system
                pass
    
    # Call the function to add Persian
    add_persian_language()

# Override the preferences screen to include Persian
init 1000 python:
    # Custom language preference that includes Persian
    def get_available_languages():
        """Get list of available languages including Persian"""
        languages = []
        
        # Add default language (English)
        languages.append((config.language_names.get(None, "English"), None))
        
        # Add translated languages that exist
        for lang_code in ["es", "pt", "ru", "fr", "sv", "fa"]:
            if lang_code in renpy.known_languages() or lang_code == "fa":
                lang_name = config.language_names.get(lang_code, lang_code)
                languages.append((lang_name, lang_code))
        
        return languages

# Screen override to add Persian language option
screen preferences():
    tag menu
    
    use game_menu(_("Preferences")):
        
        vbox:
            spacing 10
            
            hbox:
                spacing 20
                
                vbox:
                    spacing 10
                    
                    label _("Display")
                    textbutton _("Window") action Preference("display", "window")
                    textbutton _("Fullscreen") action Preference("display", "fullscreen")
                    
                    null height 20
                    
                    label _("Language")
                    for lang_name, lang_code in get_available_languages():
                        textbutton lang_name action Language(lang_code)
                
                vbox:
                    spacing 10
                    
                    label _("Music Volume")
                    bar value Preference("music volume")
                    
                    label _("Sound Volume") 
                    bar value Preference("sound volume")
                    
                    label _("Voice Volume")
                    bar value Preference("voice volume")
                    
                    textbutton _("Mute All") action Preference("all mute", "toggle")

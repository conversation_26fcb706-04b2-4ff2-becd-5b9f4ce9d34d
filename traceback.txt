﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/language_config.rpy", line 4, in script
    init -1000 python:
  File "game/language_config.rpy", line 9, in <module>
    config.language_names = {
Exception: config.language_names is not a known configuration variable.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/language_config.rpy", line 4, in script
    init -1000 python:
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/language_config.rpy", line 9, in <module>
    config.language_names = {
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.language_names is not a known configuration variable.

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Sun Aug  3 22:39:09 2025

# TODO: Translation updated at 2023-10-30 19:43

translate fa strings:

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new "گویش خودکار غیرفعال شد."

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled. "
    new "خواندن کلیپ‌بورد فعال شد."

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled. "
    new "گویش خودکار فعال شد."

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new "نوار"

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new "انتخاب شده"

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new "نمایشگر"

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new "اسکرول افقی"

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new "اسکرول عمودی"

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new "فعال‌سازی"

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new "غیرفعال‌سازی"

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new "افزایش"

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new "کاهش"

    # renpy/common/00accessibility.rpy:138
    old "Font Override"
    new "تغییر فونت"

    # renpy/common/00accessibility.rpy:142
    old "Default"
    new "پیش‌فرض"

    # renpy/common/00accessibility.rpy:146
    old "DejaVu Sans"
    new "DejaVu Sans"

    # renpy/common/00accessibility.rpy:150
    old "Opendyslexic"
    new "Opendyslexic"

    # renpy/common/00accessibility.rpy:156
    old "Text Size Scaling"
    new "مقیاس اندازه متن"

    # renpy/common/00accessibility.rpy:162
    old "Reset"
    new "بازنشانی"

    # renpy/common/00accessibility.rpy:168
    old "Line Spacing Scaling"
    new "مقیاس فاصله خطوط"

    # renpy/common/00accessibility.rpy:180
    old "High Contrast Text"
    new "متن با کنتراست بالا"

    # renpy/common/00accessibility.rpy:182
    old "Enable"
    new "فعال‌سازی"

    # renpy/common/00accessibility.rpy:186
    old "Disable"
    new "غیرفعال‌سازی"

    # renpy/common/00accessibility.rpy:193
    old "Self-Voicing"
    new "گویش خودکار"

    # renpy/common/00accessibility.rpy:197
    old "Off"
    new "خاموش"

    # renpy/common/00accessibility.rpy:201
    old "Text-to-speech"
    new "تبدیل متن به گفتار"

    # renpy/common/00accessibility.rpy:205
    old "Clipboard"
    new "کلیپ‌بورد"

    # renpy/common/00accessibility.rpy:209
    old "Debug"
    new "اشکال‌زدایی"

    # renpy/common/00accessibility.rpy:215
    old "Voice Volume"
    new "حجم صدا"

    # renpy/common/00accessibility.rpy:223
    old "Self-Voicing Volume Drop"
    new "کاهش حجم صدا هنگام گویش خودکار"

    # renpy/common/00accessibility.rpy:234
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new "گزینه‌های این منو برای بهبود دسترسی طراحی شده‌اند. ممکن است با همه بازی‌ها کار نکنند و برخی ترکیب‌ها باعث غیرقابل بازی شدن شوند. این مشکل بازی یا موتور نیست. برای بهترین نتیجه هنگام تغییر فونت، سعی کنید اندازه متن را ثابت نگه دارید."

    # renpy/common/00accessibility.rpy:239
    old "Return"
    new "بازگشت"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new "{#weekday}دوشنبه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new "{#weekday}سه‌شنبه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new "{#weekday}چهارشنبه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new "{#weekday}پنج‌شنبه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new "{#weekday}جمعه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new "{#weekday}شنبه"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new "{#weekday}یک‌شنبه"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new "{#weekday_short}دو"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new "{#weekday_short}سه"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new "{#weekday_short}چه"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new "{#weekday_short}پن"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new "{#weekday_short}جم"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new "{#weekday_short}شن"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new "{#weekday_short}یک"

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new "{#month}ژانویه"

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new "{#month}فوریه"

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new "{#month}مارس"

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new "{#month}آوریل"

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new "{#month}مه"

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new "{#month}ژوئن"

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new "{#month}ژوئیه"

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new "{#month}اوت"

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new "{#month}سپتامبر"

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new "{#month}اکتبر"

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new "{#month}نوامبر"

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new "{#month}دسامبر"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new "{#month_short}ژان"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new "{#month_short}فور"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new "{#month_short}مار"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new "{#month_short}آور"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new "{#month_short}مه"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new "{#month_short}ژو"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new "{#month_short}ژو"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new "{#month_short}اوت"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new "{#month_short}سپت"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new "{#month_short}اکت"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new "{#month_short}نوا"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new "{#month_short}دسا"

    # renpy/common/00action_file.rpy:258
    old "%b %d, %H:%M"
    new "%b %d, %H:%M"

    # renpy/common/00action_file.rpy:378
    old "Save slot %s: [text]"
    new "Save slot %s: [text]"

    # renpy/common/00action_file.rpy:461
    old "Load slot %s: [text]"
    new "Load slot %s: [text]"

    # renpy/common/00action_file.rpy:514
    old "Delete slot [text]"
    new "Delete slot [text]"

    # renpy/common/00action_file.rpy:593
    old "File page auto"
    new "File page auto"

    # renpy/common/00action_file.rpy:595
    old "File page quick"
    new "File page quick"

    # renpy/common/00action_file.rpy:597
    old "File page [text]"
    new "File page [text]"

    # renpy/common/00action_file.rpy:655
    old "Page {}"
    new "Page {}"

    # renpy/common/00action_file.rpy:655
    old "Automatic saves"
    new "Automatic saves"

    # renpy/common/00action_file.rpy:655
    old "Quick saves"
    new "Quick saves"

    # renpy/common/00action_file.rpy:796
    old "Next file page."
    new "Next file page."

    # renpy/common/00action_file.rpy:868
    old "Previous file page."
    new "Previous file page."

    # renpy/common/00action_file.rpy:929
    old "Quick save complete."
    new "Quick save complete."

    # renpy/common/00action_file.rpy:944
    old "Quick save."
    new "Quick save."

    # renpy/common/00action_file.rpy:963
    old "Quick load."
    new "Quick load."

    # renpy/common/00action_other.rpy:381
    old "Language [text]"
    new "Language [text]"

    # renpy/common/00action_other.rpy:721
    old "Open [text] directory."
    new "Open [text] directory."

    # renpy/common/00director.rpy:705
    old "The interactive director is not enabled here."
    new "The interactive director is not enabled here."

    # renpy/common/00director.rpy:1504
    old "⬆"
    new "⬆"

    # renpy/common/00director.rpy:1510
    old "⬇"
    new "⬇"

    # renpy/common/00director.rpy:1574
    old "Done"
    new "Done"

    # renpy/common/00director.rpy:1584
    old "(statement)"
    new "(statement)"

    # renpy/common/00director.rpy:1585
    old "(tag)"
    new "(tag)"

    # renpy/common/00director.rpy:1586
    old "(attributes)"
    new "(attributes)"

    # renpy/common/00director.rpy:1587
    old "(transform)"
    new "(transform)"

    # renpy/common/00director.rpy:1612
    old "(transition)"
    new "(transition)"

    # renpy/common/00director.rpy:1624
    old "(channel)"
    new "(channel)"

    # renpy/common/00director.rpy:1625
    old "(filename)"
    new "(filename)"

    # renpy/common/00director.rpy:1654
    old "Change"
    new "Change"

    # renpy/common/00director.rpy:1656
    old "Add"
    new "Add"

    # renpy/common/00director.rpy:1659
    old "Cancel"
    new "Cancel"

    # renpy/common/00director.rpy:1662
    old "Remove"
    new "Remove"

    # renpy/common/00director.rpy:1697
    old "Statement:"
    new "Statement:"

    # renpy/common/00director.rpy:1718
    old "Tag:"
    new "Tag:"

    # renpy/common/00director.rpy:1734
    old "Attributes:"
    new "Attributes:"

    # renpy/common/00director.rpy:1745
    old "Click to toggle attribute, right click to toggle negative attribute."
    new "Click to toggle attribute, right click to toggle negative attribute."

    # renpy/common/00director.rpy:1757
    old "Transforms:"
    new "Transforms:"

    # renpy/common/00director.rpy:1768
    old "Click to set transform, right click to add to transform list."
    new "Click to set transform, right click to add to transform list."

    # renpy/common/00director.rpy:1780
    old "Behind:"
    new "Behind:"

    # renpy/common/00director.rpy:1789
    old "Click to set, right click to add to behind list."
    new "Click to set, right click to add to behind list."

    # renpy/common/00director.rpy:1801
    old "Transition:"
    new "Transition:"

    # renpy/common/00director.rpy:1819
    old "Channel:"
    new "Channel:"

    # renpy/common/00director.rpy:1837
    old "Audio Filename:"
    new "Audio Filename:"

    # renpy/common/00gui.rpy:446
    old "Are you sure?"
    new "Are you sure?"

    # renpy/common/00gui.rpy:447
    old "Are you sure you want to delete this save?"
    new "Are you sure you want to delete this save?"

    # renpy/common/00gui.rpy:448
    old "Are you sure you want to overwrite your save?"
    new "Are you sure you want to overwrite your save?"

    # renpy/common/00gui.rpy:449
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new "Loading will lose unsaved progress.\nAre you sure you want to do this?"

    # renpy/common/00gui.rpy:450
    old "Are you sure you want to quit?"
    new "Are you sure you want to quit?"

    # renpy/common/00gui.rpy:451
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."

    # renpy/common/00gui.rpy:452
    old "Are you sure you want to end the replay?"
    new "Are you sure you want to end the replay?"

    # renpy/common/00gui.rpy:453
    old "Are you sure you want to begin skipping?"
    new "Are you sure you want to begin skipping?"

    # renpy/common/00gui.rpy:454
    old "Are you sure you want to skip to the next choice?"
    new "Are you sure you want to skip to the next choice?"

    # renpy/common/00gui.rpy:455
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new "Are you sure you want to skip unseen dialogue to the next choice?"

    # renpy/common/00gui.rpy:456
    old "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"
    new "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"

    # renpy/common/00gui.rpy:457
    old "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."
    new "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."

    # renpy/common/00keymap.rpy:322
    old "Failed to save screenshot as %s."
    new "Failed to save screenshot as %s."

    # renpy/common/00keymap.rpy:334
    old "Saved screenshot as %s."
    new "Saved screenshot as %s."

    # renpy/common/00library.rpy:248
    old "Skip Mode"
    new "Skip Mode"

    # renpy/common/00library.rpy:353
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."

    # renpy/common/00preferences.rpy:271
    old "display"
    new "display"

    # renpy/common/00preferences.rpy:283
    old "transitions"
    new "transitions"

    # renpy/common/00preferences.rpy:292
    old "skip transitions"
    new "skip transitions"

    # renpy/common/00preferences.rpy:294
    old "video sprites"
    new "video sprites"

    # renpy/common/00preferences.rpy:303
    old "show empty window"
    new "show empty window"

    # renpy/common/00preferences.rpy:312
    old "text speed"
    new "text speed"

    # renpy/common/00preferences.rpy:320
    old "joystick"
    new "joystick"

    # renpy/common/00preferences.rpy:320
    old "joystick..."
    new "joystick..."

    # renpy/common/00preferences.rpy:327
    old "skip"
    new "skip"

    # renpy/common/00preferences.rpy:330
    old "skip unseen [text]"
    new "skip unseen [text]"

    # renpy/common/00preferences.rpy:335
    old "skip unseen text"
    new "skip unseen text"

    # renpy/common/00preferences.rpy:337
    old "begin skipping"
    new "begin skipping"

    # renpy/common/00preferences.rpy:341
    old "after choices"
    new "after choices"

    # renpy/common/00preferences.rpy:348
    old "skip after choices"
    new "skip after choices"

    # renpy/common/00preferences.rpy:350
    old "auto-forward time"
    new "auto-forward time"

    # renpy/common/00preferences.rpy:364
    old "auto-forward"
    new "auto-forward"

    # renpy/common/00preferences.rpy:371
    old "Auto forward"
    new "Auto forward"

    # renpy/common/00preferences.rpy:374
    old "auto-forward after click"
    new "auto-forward after click"

    # renpy/common/00preferences.rpy:383
    old "automatic move"
    new "automatic move"

    # renpy/common/00preferences.rpy:392
    old "wait for voice"
    new "wait for voice"

    # renpy/common/00preferences.rpy:401
    old "voice sustain"
    new "voice sustain"

    # renpy/common/00preferences.rpy:410
    old "self voicing"
    new "self voicing"

    # renpy/common/00preferences.rpy:419
    old "self voicing volume drop"
    new "self voicing volume drop"

    # renpy/common/00preferences.rpy:427
    old "clipboard voicing"
    new "clipboard voicing"

    # renpy/common/00preferences.rpy:436
    old "debug voicing"
    new "debug voicing"

    # renpy/common/00preferences.rpy:445
    old "emphasize audio"
    new "emphasize audio"

    # renpy/common/00preferences.rpy:454
    old "rollback side"
    new "rollback side"

    # renpy/common/00preferences.rpy:464
    old "gl powersave"
    new "gl powersave"

    # renpy/common/00preferences.rpy:470
    old "gl framerate"
    new "gl framerate"

    # renpy/common/00preferences.rpy:473
    old "gl tearing"
    new "gl tearing"

    # renpy/common/00preferences.rpy:476
    old "font transform"
    new "font transform"

    # renpy/common/00preferences.rpy:479
    old "font size"
    new "font size"

    # renpy/common/00preferences.rpy:487
    old "font line spacing"
    new "font line spacing"

    # renpy/common/00preferences.rpy:495
    old "system cursor"
    new "system cursor"

    # renpy/common/00preferences.rpy:504
    old "renderer menu"
    new "renderer menu"

    # renpy/common/00preferences.rpy:507
    old "accessibility menu"
    new "accessibility menu"

    # renpy/common/00preferences.rpy:510
    old "high contrast text"
    new "high contrast text"

    # renpy/common/00preferences.rpy:519
    old "audio when minimized"
    new "audio when minimized"

    # renpy/common/00preferences.rpy:528
    old "audio when unfocused"
    new "audio when unfocused"

    # renpy/common/00preferences.rpy:537
    old "web cache preload"
    new "web cache preload"

    # renpy/common/00preferences.rpy:552
    old "voice after game menu"
    new "voice after game menu"

    # renpy/common/00preferences.rpy:571
    old "main volume"
    new "main volume"

    # renpy/common/00preferences.rpy:572
    old "music volume"
    new "music volume"

    # renpy/common/00preferences.rpy:573
    old "sound volume"
    new "sound volume"

    # renpy/common/00preferences.rpy:574
    old "voice volume"
    new "voice volume"

    # renpy/common/00preferences.rpy:575
    old "mute main"
    new "mute main"

    # renpy/common/00preferences.rpy:576
    old "mute music"
    new "mute music"

    # renpy/common/00preferences.rpy:577
    old "mute sound"
    new "mute sound"

    # renpy/common/00preferences.rpy:578
    old "mute voice"
    new "mute voice"

    # renpy/common/00preferences.rpy:579
    old "mute all"
    new "mute all"

    # renpy/common/00preferences.rpy:653
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new "Clipboard voicing enabled. Press 'shift+C' to disable."

    # renpy/common/00preferences.rpy:655
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."

    # renpy/common/00preferences.rpy:657
    old "Self-voicing enabled. Press 'v' to disable."
    new "Self-voicing enabled. Press 'v' to disable."

    # renpy/common/00speechbubble.rpy:344
    old "Speech Bubble Editor"
    new "Speech Bubble Editor"

    # renpy/common/00speechbubble.rpy:349
    old "(hide)"
    new "(hide)"

    # renpy/common/00sync.rpy:70
    old "Sync downloaded."
    new "Sync downloaded."

    # renpy/common/00sync.rpy:190
    old "Could not connect to the Ren'Py Sync server."
    new "Could not connect to the Ren'Py Sync server."

    # renpy/common/00sync.rpy:192
    old "The Ren'Py Sync server timed out."
    new "The Ren'Py Sync server timed out."

    # renpy/common/00sync.rpy:194
    old "An unknown error occurred while connecting to the Ren'Py Sync server."
    new "An unknown error occurred while connecting to the Ren'Py Sync server."

    # renpy/common/00sync.rpy:267
    old "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."
    new "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."

    # renpy/common/00sync.rpy:409
    old "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."
    new "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."

    # renpy/common/00sync.rpy:428
    old "The sync ID is not in the correct format."
    new "The sync ID is not in the correct format."

    # renpy/common/00sync.rpy:448
    old "The sync could not be decrypted."
    new "The sync could not be decrypted."

    # renpy/common/00sync.rpy:471
    old "The sync belongs to a different game."
    new "The sync belongs to a different game."

    # renpy/common/00sync.rpy:476
    old "The sync contains a file with an invalid name."
    new "The sync contains a file with an invalid name."

    # renpy/common/00sync.rpy:529
    old "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"
    new "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"

    # renpy/common/00sync.rpy:537
    old "Yes"
    new "بله"

    # renpy/common/00sync.rpy:538
    old "No"
    new "خیر"

    # renpy/common/00sync.rpy:558
    old "Enter Sync ID"
    new "Enter Sync ID"

    # renpy/common/00sync.rpy:569
    old "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."
    new "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."

    # renpy/common/00sync.rpy:596
    old "Sync Success"
    new "Sync Success"

    # renpy/common/00sync.rpy:599
    old "The Sync ID is:"
    new "The Sync ID is:"

    # renpy/common/00sync.rpy:605
    old "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."
    new "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."

    # renpy/common/00sync.rpy:609
    old "Continue"
    new "Continue"

    # renpy/common/00sync.rpy:631
    old "Sync Error"
    new "Sync Error"

    # renpy/common/00iap.rpy:219
    old "Contacting App Store\nPlease Wait..."
    new "Contacting App Store\nPlease Wait..."

    # renpy/common/00updater.rpy:419
    old "The Ren'Py Updater is not supported on mobile devices."
    new "The Ren'Py Updater is not supported on mobile devices."

    # renpy/common/00updater.rpy:548
    old "An error is being simulated."
    new "An error is being simulated."

    # renpy/common/00updater.rpy:738
    old "Either this project does not support updating, or the update status file was deleted."
    new "Either this project does not support updating, or the update status file was deleted."

    # renpy/common/00updater.rpy:752
    old "This account does not have permission to perform an update."
    new "This account does not have permission to perform an update."

    # renpy/common/00updater.rpy:755
    old "This account does not have permission to write the update log."
    new "This account does not have permission to write the update log."

    # renpy/common/00updater.rpy:783
    old "Could not verify update signature."
    new "Could not verify update signature."

    # renpy/common/00updater.rpy:1084
    old "The update file was not downloaded."
    new "The update file was not downloaded."

    # renpy/common/00updater.rpy:1102
    old "The update file does not have the correct digest - it may have been corrupted."
    new "The update file does not have the correct digest - it may have been corrupted."

    # renpy/common/00updater.rpy:1252
    old "While unpacking {}, unknown type {}."
    new "While unpacking {}, unknown type {}."

    # renpy/common/00updater.rpy:1624
    old "Updater"
    new "Updater"

    # renpy/common/00updater.rpy:1631
    old "An error has occured:"
    new "An error has occured:"

    # renpy/common/00updater.rpy:1633
    old "Checking for updates."
    new "Checking for updates."

    # renpy/common/00updater.rpy:1635
    old "This program is up to date."
    new "This program is up to date."

    # renpy/common/00updater.rpy:1637
    old "[u.version] is available. Do you want to install it?"
    new "[u.version] is available. Do you want to install it?"

    # renpy/common/00updater.rpy:1639
    old "Preparing to download the updates."
    new "Preparing to download the updates."

    # renpy/common/00updater.rpy:1641
    old "Downloading the updates."
    new "Downloading the updates."

    # renpy/common/00updater.rpy:1643
    old "Unpacking the updates."
    new "Unpacking the updates."

    # renpy/common/00updater.rpy:1645
    old "Finishing up."
    new "Finishing up."

    # renpy/common/00updater.rpy:1647
    old "The updates have been installed. The program will restart."
    new "The updates have been installed. The program will restart."

    # renpy/common/00updater.rpy:1649
    old "The updates have been installed."
    new "The updates have been installed."

    # renpy/common/00updater.rpy:1651
    old "The updates were cancelled."
    new "The updates were cancelled."

    # renpy/common/00updater.rpy:1666
    old "Proceed"
    new "Proceed"

    # renpy/common/00gallery.rpy:627
    old "Image [index] of [count] locked."
    new "Image [index] of [count] locked."

    # renpy/common/00gallery.rpy:647
    old "prev"
    new "prev"

    # renpy/common/00gallery.rpy:648
    old "next"
    new "next"

    # renpy/common/00gallery.rpy:649
    old "slideshow"
    new "slideshow"

    # renpy/common/00gallery.rpy:650
    old "return"
    new "return"

    # renpy/common/00gltest.rpy:89
    old "Renderer"
    new "Renderer"

    # renpy/common/00gltest.rpy:93
    old "Automatically Choose"
    new "Automatically Choose"

    # renpy/common/00gltest.rpy:100
    old "Force GL Renderer"
    new "Force GL Renderer"

    # renpy/common/00gltest.rpy:105
    old "Force ANGLE Renderer"
    new "Force ANGLE Renderer"

    # renpy/common/00gltest.rpy:110
    old "Force GLES Renderer"
    new "Force GLES Renderer"

    # renpy/common/00gltest.rpy:116
    old "Force GL2 Renderer"
    new "Force GL2 Renderer"

    # renpy/common/00gltest.rpy:121
    old "Force ANGLE2 Renderer"
    new "Force ANGLE2 Renderer"

    # renpy/common/00gltest.rpy:126
    old "Force GLES2 Renderer"
    new "Force GLES2 Renderer"

    # renpy/common/00gltest.rpy:132
    old "Gamepad"
    new "Gamepad"

    # renpy/common/00gltest.rpy:136
    old "Enable (No Blocklist)"
    new "Enable (No Blocklist)"

    # renpy/common/00gltest.rpy:150
    old "Calibrate"
    new "Calibrate"

    # renpy/common/00gltest.rpy:159
    old "Powersave"
    new "Powersave"

    # renpy/common/00gltest.rpy:173
    old "Framerate"
    new "Framerate"

    # renpy/common/00gltest.rpy:177
    old "Screen"
    new "Screen"

    # renpy/common/00gltest.rpy:181
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:185
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:191
    old "Tearing"
    new "Tearing"

    # renpy/common/00gltest.rpy:207
    old "Changes will take effect the next time this program is run."
    new "Changes will take effect the next time this program is run."

    # renpy/common/00gltest.rpy:242
    old "Performance Warning"
    new "Performance Warning"

    # renpy/common/00gltest.rpy:247
    old "This computer is using software rendering."
    new "This computer is using software rendering."

    # renpy/common/00gltest.rpy:249
    old "This game requires use of GL2 that can't be initialised."
    new "This game requires use of GL2 that can't be initialised."

    # renpy/common/00gltest.rpy:251
    old "This computer has a problem displaying graphics: [problem]."
    new "This computer has a problem displaying graphics: [problem]."

    # renpy/common/00gltest.rpy:255
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."

    # renpy/common/00gltest.rpy:259
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    new "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."

    # renpy/common/00gltest.rpy:264
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    new "More details on how to fix this can be found in the {a=[url]}documentation{/a}."

    # renpy/common/00gltest.rpy:269
    old "Continue, Show this warning again"
    new "Continue, Show this warning again"

    # renpy/common/00gltest.rpy:273
    old "Continue, Don't show warning again"
    new "Continue, Don't show warning again"

    # renpy/common/00gltest.rpy:281
    old "Change render options"
    new "Change render options"

    # renpy/common/00gamepad.rpy:32
    old "Select Gamepad to Calibrate"
    new "Select Gamepad to Calibrate"

    # renpy/common/00gamepad.rpy:35
    old "No Gamepads Available"
    new "No Gamepads Available"

    # renpy/common/00gamepad.rpy:54
    old "Calibrating [name] ([i]/[total])"
    new "Calibrating [name] ([i]/[total])"

    # renpy/common/00gamepad.rpy:58
    old "Press or move the '[control!s]' [kind]."
    new "Press or move the '[control!s]' [kind]."

    # renpy/common/00gamepad.rpy:68
    old "Skip (A)"
    new "Skip (A)"

    # renpy/common/00gamepad.rpy:71
    old "Back (B)"
    new "Back (B)"

    # renpy/common/_errorhandling.rpym:555
    old "Open"
    new "Open"

    # renpy/common/_errorhandling.rpym:557
    old "Opens the traceback.txt file in a text editor."
    new "Opens the traceback.txt file in a text editor."

    # renpy/common/_errorhandling.rpym:559
    old "Copy BBCode"
    new "Copy BBCode"

    # renpy/common/_errorhandling.rpym:561
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:563
    old "Copy Markdown"
    new "Copy Markdown"

    # renpy/common/_errorhandling.rpym:565
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new "Copies the traceback.txt file to the clipboard as Markdown for Discord."

    # renpy/common/_errorhandling.rpym:594
    old "An exception has occurred."
    new "An exception has occurred."

    # renpy/common/_errorhandling.rpym:617
    old "Rollback"
    new "Rollback"

    # renpy/common/_errorhandling.rpym:619
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "Attempts a roll back to a prior time, allowing you to save or choose a different choice."

    # renpy/common/_errorhandling.rpym:622
    old "Ignore"
    new "Ignore"

    # renpy/common/_errorhandling.rpym:626
    old "Ignores the exception, allowing you to continue."
    new "Ignores the exception, allowing you to continue."

    # renpy/common/_errorhandling.rpym:628
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "Ignores the exception, allowing you to continue. This often leads to additional errors."

    # renpy/common/_errorhandling.rpym:632
    old "Reload"
    new "Reload"

    # renpy/common/_errorhandling.rpym:634
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "Reloads the game from disk, saving and restoring game state if possible."

    # renpy/common/_errorhandling.rpym:637
    old "Console"
    new "Console"

    # renpy/common/_errorhandling.rpym:639
    old "Opens a console to allow debugging the problem."
    new "Opens a console to allow debugging the problem."

    # renpy/common/_errorhandling.rpym:652
    old "Quits the game."
    new "Quits the game."

    # renpy/common/_errorhandling.rpym:673
    old "Parsing the script failed."
    new "Parsing the script failed."

# TODO: Translation updated at 2024-03-28 16:33

translate fa strings:

    # renpy/common/00accessibility.rpy:120
    old "Accessibility Menu. Use up and down arrows to navigate, and enter to activate buttons and bars."
    new "منوی دسترسی. از فلش‌های بالا و پایین برای حرکت و Enter برای فعال‌سازی دکمه‌ها و نوارها استفاده کنید."

    # renpy/common/00director.rpy:1776
    old "Customize director.transforms to add more transforms."
    new "director.transforms را سفارشی کنید تا تبدیل‌های بیشتری اضافه کنید."

    # renpy/common/00director.rpy:1821
    old "Click to set."
    new "برای تنظیم کلیک کنید."

    # renpy/common/00director.rpy:1822
    old "Customize director.transitions to add more transitions."
    new "director.transitions را سفارشی کنید تا انتقال‌های بیشتری اضافه کنید."

    # renpy/common/00director.rpy:1845
    old "Customize director.audio_channels to add more channels."
    new "director.audio_channels را سفارشی کنید تا کانال‌های بیشتری اضافه کنید."

    # renpy/common/00gui.rpy:454
    old "Are you sure you want to continue where you left off?"
    new "آیا مطمئن هستید که می‌خواهید از جایی که رها کرده‌اید ادامه دهید؟"

    # renpy/common/00preferences.rpy:438
    old "self voicing enable"
    new "فعال‌سازی گویش خودکار"

    # renpy/common/00preferences.rpy:440
    old "self voicing disable"
    new "غیرفعال‌سازی گویش خودکار"

    # renpy/common/00preferences.rpy:455
    old "clipboard voicing enable"
    new "فعال‌سازی خواندن کلیپ‌بورد"

    # renpy/common/00preferences.rpy:457
    old "clipboard voicing disable"
    new "غیرفعال‌سازی خواندن کلیپ‌بورد"

    # renpy/common/00preferences.rpy:464
    old "debug voicing enable"
    new "فعال‌سازی گویش اشکال‌زدایی"

    # renpy/common/00preferences.rpy:466
    old "debug voicing disable"
    new "غیرفعال‌سازی گویش اشکال‌زدایی"

    # renpy/common/00preferences.rpy:586
    old "restore window position"
    new "بازیابی موقعیت پنجره"

    # renpy/common/00preferences.rpy:595
    old "reset"
    new "بازنشانی"

    # renpy/common/00speechbubble.rpy:408
    old "(clear retained bubbles)"
    new "(پاک کردن حباب‌های نگه‌داشته شده)"

    # renpy/common/00updater.rpy:505
    old "No update methods found."
    new "هیچ روش به‌روزرسانی یافت نشد."

    # renpy/common/00updater.rpy:552
    old "Could not download file list: "
    new "دانلود لیست فایل‌ها ممکن نیست:"

    # renpy/common/00updater.rpy:555
    old "File list digest does not match."
    new "خلاصه لیست فایل‌ها مطابقت ندارد."

    # renpy/common/00updater.rpy:2072
    old "Preparing to download the game data."
    new "در حال آماده‌سازی برای دانلود اطلاعات بازی."

    # renpy/common/00updater.rpy:2074
    old "Downloading the game data."
    new "در حال دانلود اطلاعات بازی."

    # renpy/common/00updater.rpy:2076
    old "The game data has been downloaded."
    new "اطلاعات بازی دانلود شد."

    # renpy/common/00updater.rpy:2078
    old "An error occured when trying to download game data:"
    new "هنگام تلاش برای دانلود اطلاعات بازی خطایی رخ داد:"

    # renpy/common/00updater.rpy:2083
    old "This game cannot be run until the game data has been downloaded."
    new "این بازی تا زمانی که اطلاعات آن دانلود نشود قابل اجرا نیست."

    # renpy/common/00updater.rpy:2090
    old "Retry"
    new "تلاش مجدد"


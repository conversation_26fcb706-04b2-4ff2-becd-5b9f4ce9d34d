# Persian Font Configuration
init -2 python:
    # Configure fonts for Persian language
    
    # Enable font substitution
    config.font_hinting = "auto"
    config.font_replacement_map = {}
    
    # Try to use system fonts that support Persian
    import os
    
    # List of fonts that support Persian/Arabic script
    persian_fonts = [
        "arial.ttf",
        "tahoma.ttf", 
        "calibri.ttf",
        "segoeui.ttf",
        "times.ttf"
    ]
    
    # Find available Persian font
    persian_font = None
    for font in persian_fonts:
        try:
            # Try to use the font
            persian_font = font
            break
        except:
            continue
    
    if persian_font:
        # Map all fonts to Persian-supporting font
        config.font_replacement_map = {
            ("DejaVuSans.ttf", False, False): (persian_font, False, False),
            ("DejaVuSans.ttf", True, False): (persian_font, True, False),
            ("DejaVuSans.ttf", False, True): (persian_font, False, True),
            ("DejaVuSans.ttf", True, True): (persian_font, True, True),
        }

# Alternative: Define Persian-specific styles
init -1:
    # Persian text style
    style persian_text:
        font "arial.ttf"
        size 18
        color "#ffffff"
        
    style persian_button_text:
        font "arial.ttf" 
        size 16
        color "#ffffff"

# Override default styles for Persian
init python:
    def apply_persian_styles():
        if _preferences.language == "fa":
            # Apply Persian styles when Persian is selected
            style.default.font = "arial.ttf"
            style.button_text.font = "arial.ttf"
            style.text.font = "arial.ttf"

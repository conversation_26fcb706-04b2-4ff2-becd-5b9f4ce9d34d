2025-08-03 18:18:09 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Becoming a Femboy
3.6.2-default
Built at 2025-06-19 17:46:17 UTC

Early init took 0.40s
Loading error handling took 0.11s
Loading script took 2.66s
Loading save slot metadata took 0.07s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)

Full traceback:
  File "game/persian_font.rpyc", line 54, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/persian_font.rpy", line 63, in <module>
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.after_change_language_callbacks is not a known configuration variable.

While running game code:
  File "game/persian_font.rpy", line 63, in <module>
Exception: config.after_change_language_callbacks is not a known configuration variable.
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Interface start took 0.20s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Maximum texture size: 4096x4096
While handling exception:
Traceback (most recent call last):
  File "C:/Users\The Doctor\Music\362/renpy/execution.py", line 599, in run
    node.execute()
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/persian_font.rpy", line 63, in <module>
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.after_change_language_callbacks is not a known configuration variable.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:/Users\The Doctor\Music\362/renpy/display/error.py", line 140, in report_exception
    renpy.game.invoke_in_new_context(
  File "C:/Users\The Doctor\Music\362/renpy/game.py", line 303, in invoke_in_new_context
    return callable(*args, **kwargs)
  File "C:/Users\The Doctor\Music\362/renpy/display/error.py", line 48, in call_exception_screen
    return renpy.ui.interact(mouse="screen", type="screen", suppress_overlay=True, suppress_underlay=True)
  File "C:/Users\The Doctor\Music\362/renpy/ui.py", line 301, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1974, in render
    cwidth, cheight = sizeit('c', width, height, 0, 0)
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1971, in sizeit
    rend = renpy.display.render.render_for_size(pos_d[pos], width, height, st, at)
  File "render.pyx", line 312, in renpy.display.render.render_for_size
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1974, in render
    cwidth, cheight = sizeit('c', width, height, 0, 0)
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1971, in sizeit
    rend = renpy.display.render.render_for_size(pos_d[pos], width, height, st, at)
  File "render.pyx", line 312, in renpy.display.render.render_for_size
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/viewport.py", line 287, in render
    surf = renpy.display.render.render(self.child, child_width, child_height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

Full traceback:
  File "C:/Users\The Doctor\Music\362/renpy/bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 541, in main
    renpy.game.context().run(node)
  File "game/persian_font.rpyc", line 54, in script
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "game/persian_font.rpyc", line 54, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/persian_font.rpy", line 63, in <module>
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.after_change_language_callbacks is not a known configuration variable.

While running game code:
  File "game/persian_font.rpy", line 63, in <module>
Exception: config.after_change_language_callbacks is not a known configuration variable.

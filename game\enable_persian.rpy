# Enable Persian Language with Font Support
init python:
    # Simple function to enable Persian
    def enable_persian():
        original_known = renpy.known_languages
        def new_known():
            langs = list(original_known())
            if "fa" not in langs:
                langs.append("fa")
            return langs
        renpy.known_languages = new_known

    enable_persian()

# Persian font configuration
init -1 python:
    # Simple font setup for Persian
    # Use Arial font which supports Persian
    config.font_replacement_map = {
        ("DejaVuSans.ttf", False, False): ("arial.ttf", False, False),
        ("DejaVuSans.ttf", True, False): ("arial.ttf", True, False),
        ("DejaVuSans.ttf", False, True): ("arial.ttf", False, True),
        ("DejaVuSans.ttf", True, True): ("arial.ttf", True, True),
    }

# Add a simple way to switch to Persian
label start_persian:
    $ renpy.change_language("fa")
    return

# Create a simple screen for language selection
screen persian_language_menu():
    modal True
    frame:
        xalign 0.5
        yalign 0.5
        vbox:
            text "انتخاب زبان / Select Language"
            textbutton "فارسی" action [Language("fa"), Hide("persian_language_menu")]
            textbutton "English" action [Language(None), Hide("persian_language_menu")]
            textbutton "Español" action [Language("es"), Hide("persian_language_menu")]
            textbutton "بستن / Close" action Hide("persian_language_menu")

# Add key binding
init:
    $ config.keymap['persian_menu'] = ['shift_K_p']
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))

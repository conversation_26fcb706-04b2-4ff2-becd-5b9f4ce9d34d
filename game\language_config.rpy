# Persian Language Support
# This file adds Persian (Farsi) language support to the game

init -1000 python:
    # Force Persian to be recognized as a known language
    import renpy.translation

    # Add Persian to the translator's language list
    def add_persian_support():
        # Get the current translator
        translator = renpy.game.script.translator

        # Add Persian to known languages if not already there
        if hasattr(translator, 'languages'):
            if 'fa' not in translator.languages:
                translator.languages.add('fa')

        # Also patch the known_languages function
        original_known_languages = renpy.known_languages

        def patched_known_languages():
            langs = set(original_known_languages())
            langs.add("fa")
            return langs

        renpy.known_languages = patched_known_languages

    # Call the function
    add_persian_support()

# Create a simple way to switch to Persian
init 1000:
    # Add a key binding to switch to Persian
    key "shift_K_f" action Language("fa")

# Add Persian language option to preferences
init 1001 python:
    # Override the preferences screen to include Persian
    def show_persian_option():
        # Create a simple textbutton for Persian
        renpy.display_menu([
            ("English", Language(None)),
            ("<PERSON>spa<PERSON>l", Language("es")),
            ("Português", Language("pt")),
            ("Русский", Language("ru")),
            ("Français", Language("fr")),
            ("Svenska", Language("sv")),
            ("فارسی", Language("fa"))
        ])

    # Add this as a custom action
    config.keymap['persian_menu'] = ['shift_K_l']
    config.underlay.append(renpy.Keymap(persian_menu=show_persian_option))

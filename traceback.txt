﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
AttributeError: 'str' object has no attribute 'get'

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "C:/Users\The Doctor\Music\362/renpy/bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 652, in main
    run(restart)
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 148, in run
    renpy.execution.run_context(True)
  File "C:/Users\The Doctor\Music\362/renpy/execution.py", line 958, in run_context
    context.run()
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "scripts/screens/splashscreen.rpyc", line 5, in script
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "renpy/common/00start.rpy", line 244, in script call
    call _splashscreen from _call_splashscreen_1
  File "scripts/screens/splashscreen.rpyc", line 5, in script
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 1374, in execute
    renpy.exports.with_statement(trans, paired=paired)
  File "C:/Users\The Doctor\Music\362/renpy/exports/statementexports.py", line 257, in with_statement
    return renpy.game.interface.do_with(trans, paired, clear=clear)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1581, in do_with
    return self.interact(trans_pause=True,
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 2882, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:/Users\The Doctor\Music\362/renpy/display/core.py", line 1384, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/transition.py", line 384, in render
    top = render(self.new_widget, width, height, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/screen.py", line 755, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/display/layout.py", line 1171, in render
    surf = render(d, width - x, rh, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 2627, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 776, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:/Users\The Doctor\Music\362/renpy/text/text.py", line 348, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:/Users\The Doctor\Music\362/renpy/text/font.py", line 739, in get_font
    hinting = renpy.config.font_hinting.get(fn, True)
AttributeError: 'str' object has no attribute 'get'

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Sun Aug  3 22:49:14 2025
